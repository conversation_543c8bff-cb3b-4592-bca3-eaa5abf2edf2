# Kenyan Payroll Management System - Git Ignore

# PHP
*.log
*.tmp
*.cache

# Database
*.sql.backup
*.db

# Configuration files with sensitive data
config/database_local.php
config/secrets.php

# Uploads and user files
uploads/*
!uploads/.gitkeep

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Temporary files
tmp/
temp/

# Backup files
*.bak
*.backup

# Environment files
.env
.env.local
.env.production

# Composer (if used later)
vendor/
composer.lock

# Node modules (if used later)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build files
dist/
build/
