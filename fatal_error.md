Fatal error: Uncaught PDOException: SQLSTATE[42000]: Syntax error or access violation: 1140 In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'kenyan_payroll.payroll_periods.period_name'; this is incompatible with sql_mode=only_full_group_by in /Applications/MAMP/htdocs/kenyan-payroll-system/corrected_date_validation.php:76 Stack trace: #0 /Applications/MAMP/htdocs/kenyan-payroll-system/corrected_date_validation.php(76): PDO->prepare('\n SE...') #1 /Applications/MAMP/htdocs/kenyan-payroll-system/pages/payroll.php(57): validatePayrollDates('2025-06-01', '2025-06-28', '2025-07-28', 2) #2 /Applications/MAMP/htdocs/kenyan-payroll-system/index.php(74): include('/Applications/M...') #3 {main} thrown in /Applications/MAMP/htdocs/kenyan-payroll-system/corrected_date_validation.php on line 76 