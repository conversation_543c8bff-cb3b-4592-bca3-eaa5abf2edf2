<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kenyan Payroll Management System - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-calculator"></i> Kenyan Payroll System - DEMO
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-warning">
                    <i class="fas fa-info-circle"></i> Demo Mode - No Backend
                </span>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar bg-light border-end" style="position: fixed; top: 70px; left: 0; width: 250px; height: calc(100vh - 70px); overflow-y: auto;">
        <div class="list-group list-group-flush">
            <a href="#dashboard" class="list-group-item list-group-item-action active">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <div class="list-group-item bg-secondary text-white">
                <small><strong>EMPLOYEE MANAGEMENT</strong></small>
            </div>
            <a href="#employees" class="list-group-item list-group-item-action">
                <i class="fas fa-users"></i> Employees
            </a>
            <a href="#departments" class="list-group-item list-group-item-action">
                <i class="fas fa-building"></i> Departments
            </a>
            <div class="list-group-item bg-secondary text-white">
                <small><strong>PAYROLL MANAGEMENT</strong></small>
            </div>
            <a href="#payroll" class="list-group-item list-group-item-action">
                <i class="fas fa-calculator"></i> Payroll Processing
            </a>
            <a href="#reports" class="list-group-item list-group-item-action">
                <i class="fas fa-file-alt"></i> Reports
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div style="margin-left: 250px; padding: 20px; padding-top: 90px;">
        <!-- Dashboard Section -->
        <div id="dashboard">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-tachometer-alt"></i> Dashboard</h2>
                <div class="text-muted">Welcome to Kenyan Payroll System!</div>
            </div>

            <!-- KPI Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>25</h4>
                                    <p class="mb-0">Active Employees</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>KES 2,450,000</h4>
                                    <p class="mb-0">Monthly Payroll</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>3</h4>
                                    <p class="mb-0">Pending Leaves</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-times fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>Dec 2024</h4>
                                    <p class="mb-0">Current Period</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payroll Calculator Demo -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-calculator"></i> Kenyan Payroll Calculator Demo</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Input</h6>
                                    <div class="mb-3">
                                        <label class="form-label">Contract Type</label>
                                        <select class="form-control" id="contractType" onchange="calculatePayroll()">
                                            <option value="permanent">Permanent</option>
                                            <option value="contract">Contract (NSSF & Housing Levy Exempt)</option>
                                            <option value="casual">Casual Labourer</option>
                                            <option value="intern">Intern</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Basic Salary (KES)</label>
                                        <input type="number" class="form-control" id="basicSalary" value="75000" onchange="calculatePayroll()">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">House Allowance (KES)</label>
                                        <input type="number" class="form-control" id="houseAllowance" value="20000" onchange="calculatePayroll()">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Transport Allowance (KES)</label>
                                        <input type="number" class="form-control" id="transportAllowance" value="8000" onchange="calculatePayroll()">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Kenyan Statutory Calculations</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Gross Pay:</strong></td>
                                            <td id="grossPay">KES 103,000</td>
                                        </tr>
                                        <tr>
                                            <td>PAYE Tax:</td>
                                            <td id="paye">KES 18,180</td>
                                        </tr>
                                        <tr>
                                            <td>NSSF (6%):</td>
                                            <td id="nssf">KES 5,400</td>
                                        </tr>
                                        <tr>
                                            <td>NHIF/SHIF:</td>
                                            <td id="nhif">KES 1,700</td>
                                        </tr>
                                        <tr>
                                            <td>Housing Levy (1.5%):</td>
                                            <td id="housingLevy">KES 1,545</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td><strong>Net Pay:</strong></td>
                                            <td id="netPay"><strong>KES 76,175</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> Kenyan Tax Rates 2024</h5>
                        </div>
                        <div class="card-body">
                            <h6>PAYE Tax Brackets:</h6>
                            <ul class="list-unstyled">
                                <li>0 - 24,000: <strong>10%</strong></li>
                                <li>24,001 - 32,333: <strong>25%</strong></li>
                                <li>32,334 - 500,000: <strong>30%</strong></li>
                                <li>500,001 - 800,000: <strong>32.5%</strong></li>
                                <li>800,001+: <strong>35%</strong></li>
                            </ul>
                            <hr>
                            <h6>Other Deductions:</h6>
                            <ul class="list-unstyled">
                                <li>NSSF: <strong>6%</strong> (max KES 18,000)</li>
                                <li>Housing Levy: <strong>1.5%</strong></li>
                                <li>Personal Relief: <strong>KES 2,400</strong></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // Demo calculation function
        function calculatePayroll() {
            const contractType = document.getElementById('contractType').value;
            const basic = parseFloat(document.getElementById('basicSalary').value) || 0;
            const house = parseFloat(document.getElementById('houseAllowance').value) || 0;
            const transport = parseFloat(document.getElementById('transportAllowance').value) || 0;

            const gross = basic + house + transport;

            // NSSF calculation (exempted for contract employees)
            let nssf = 0;
            if (contractType !== 'contract') {
                nssf = Math.min(gross * 0.06, 1080); // 6% max 18000 annually = 1500 monthly
            }

            const taxableIncome = gross - nssf;

            // PAYE calculation (simplified)
            let paye = 0;
            if (taxableIncome > 24000) {
                paye += Math.min(taxableIncome - 24000, 8333) * 0.25;
                if (taxableIncome > 32333) {
                    paye += (taxableIncome - 32333) * 0.30;
                }
            } else {
                paye = taxableIncome * 0.10;
            }
            paye = Math.max(0, paye - 2400); // Personal relief

            // SHIF (Social Health Insurance Fund) - 2.75% with minimum KES 300 (applies to all)
            const calculated = gross * 0.0275; // 2.75% of gross salary
            const nhif = Math.ceil(Math.max(calculated, 300)); // Minimum KES 300, rounded up to whole number

            // Housing Levy calculation (exempted for contract employees)
            let housingLevy = 0;
            if (contractType !== 'contract') {
                housingLevy = gross * 0.015; // 1.5% of gross pay
            }

            const net = gross - paye - nssf - nhif - housingLevy;

            // Update display
            document.getElementById('grossPay').textContent = 'KES ' + gross.toLocaleString();
            document.getElementById('paye').textContent = 'KES ' + Math.round(paye).toLocaleString();
            document.getElementById('nssf').textContent = 'KES ' + Math.round(nssf).toLocaleString() + (contractType === 'contract' ? ' (Exempted)' : '');
            document.getElementById('nhif').textContent = 'KES ' + nhif.toLocaleString();
            document.getElementById('housingLevy').textContent = 'KES ' + Math.round(housingLevy).toLocaleString() + (contractType === 'contract' ? ' (Exempted)' : '');
            document.getElementById('netPay').textContent = 'KES ' + Math.round(net).toLocaleString();
        }
        
        // Calculate on page load
        calculatePayroll();
    </script>
</body>
</html>
