<?php
/**
 * Kenyan Payroll Management System
 * Main entry point for the application
 */

session_start();

// Comprehensive installation check
require_once 'includes/installation_check.php';
enforceInstallationCheck();

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'secure_auth.php';

// Simple routing
$page = $_GET['page'] ?? 'dashboard';
$action = $_GET['action'] ?? 'index';

// Initialize secure authentication
global $secureAuth;

// Check if user is logged in with enhanced security
if (!isset($_SESSION['user_id']) && $page !== 'auth') {
    header('Location: check_remember_me.php');
    exit;
}

// Validate session security for authenticated users (only if secureAuth is available)
if (isset($_SESSION['user_id']) && $secureAuth && !$secureAuth->validateSession()) {
    header('Location: check_remember_me.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kenyan Payroll Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php if (isset($_SESSION['user_id'])): ?>
        <?php include 'includes/header.php'; ?>
        <?php include 'includes/sidebar.php'; ?>
    <?php endif; ?>

    <div class="<?php echo isset($_SESSION['user_id']) ? 'main-content' : 'auth-container'; ?>">
        <?php
        // Role-based dashboard routing
        if ($page === 'dashboard' && isset($_SESSION['user_role'])) {
            switch ($_SESSION['user_role']) {
                case 'admin':
                    include 'pages/dashboard.php'; // Admin-only dashboard
                    break;
                case 'hr':
                    include 'pages/hr_dashboard.php'; // HR dashboard
                    break;
                case 'employee':
                default:
                    include 'pages/employee_dashboard.php'; // Employee dashboard
                    break;
            }
        } else {
            // Include the appropriate page
            $page_file = "pages/{$page}.php";
            if (file_exists($page_file)) {
                include $page_file;
            } else {
                include 'pages/404.php';
            }
        }
        ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
