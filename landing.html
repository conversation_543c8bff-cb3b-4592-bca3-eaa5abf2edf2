<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kenyan Payroll Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Kenyan Flag Colors */
            --kenya-black: #000000;
            --kenya-red: #ce1126;
            --kenya-white: #ffffff;
            --kenya-green: #006b3f;

            /* Enhanced <PERSON> Palette */
            --primary-green: #006b3f;
            --dark-green: #004d2e;
            --light-green: #e8f5e8;
            --accent-green: #00a651;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
        }

        /* Hero Section with Kenyan Flag Ribbon */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
            min-height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            opacity: 0.2;
        }

        /* Kenyan Flag Ribbon */
        .hero-section::after {
            content: '';
            position: absolute;
            top: -50px;
            left: -100px;
            width: 120%;
            height: 200px;
            background: linear-gradient(
                45deg,
                var(--kenya-black) 0%,
                var(--kenya-black) 20%,
                var(--kenya-red) 20%,
                var(--kenya-red) 35%,
                var(--kenya-white) 35%,
                var(--kenya-white) 50%,
                var(--kenya-red) 50%,
                var(--kenya-red) 65%,
                var(--kenya-green) 65%,
                var(--kenya-green) 100%
            );
            transform: rotate(-8deg);
            opacity: 0.15;
            z-index: 1;
        }

        /* Additional decorative ribbons */
        .kenyan-ribbon-1 {
            position: absolute;
            bottom: -50px;
            right: -100px;
            width: 120%;
            height: 150px;
            background: linear-gradient(
                -45deg,
                transparent 0%,
                var(--kenya-green) 20%,
                var(--kenya-white) 40%,
                var(--kenya-red) 60%,
                var(--kenya-black) 80%,
                transparent 100%
            );
            transform: rotate(12deg);
            opacity: 0.1;
            z-index: 1;
        }

        .kenyan-ribbon-2 {
            position: absolute;
            top: 30%;
            right: -200px;
            width: 80%;
            height: 100px;
            background: linear-gradient(
                90deg,
                transparent 0%,
                var(--kenya-red) 25%,
                var(--kenya-white) 50%,
                var(--kenya-green) 75%,
                transparent 100%
            );
            transform: rotate(-15deg);
            opacity: 0.08;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: var(--white);
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .feature-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.15);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--kenya-red), var(--accent-green));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            border: 2px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* Login Card */
        .login-card {
            background: var(--white);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            border: 1px solid rgba(16,185,129,0.1);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(
                90deg,
                var(--kenya-black) 0%,
                var(--kenya-red) 25%,
                var(--kenya-white) 50%,
                var(--kenya-green) 75%,
                var(--primary-green) 100%
            );
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: var(--text-light);
            font-size: 0.95rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid var(--gray-100);
            border-radius: 12px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--gray-50);
        }

        .form-control:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 0.2rem rgba(16,185,129,0.25);
            background: var(--white);
        }

        .form-label {
            color: var(--text-light);
            font-weight: 500;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: var(--white);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
            background-clip: padding-box;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                var(--kenya-black) 0%,
                var(--kenya-red) 25%,
                var(--kenya-white) 50%,
                var(--kenya-green) 75%,
                var(--primary-green) 100%
            );
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,107,63,0.4);
            color: var(--white);
        }

        .btn-login:hover::before {
            opacity: 0.1;
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .security-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 1.5rem;
            padding: 0.75rem;
            background: linear-gradient(
                90deg,
                var(--light-green) 0%,
                rgba(206,17,38,0.1) 25%,
                rgba(255,255,255,0.9) 50%,
                rgba(0,107,63,0.1) 75%,
                var(--light-green) 100%
            );
            border-radius: 8px;
            font-size: 0.875rem;
            color: var(--dark-green);
            border: 1px solid rgba(0,107,63,0.2);
        }

        .security-badge i {
            margin-right: 0.5rem;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        .fade-in-up-delay {
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .login-card {
                padding: 2rem;
                margin: 1rem;
            }
            
            .hero-section {
                padding: 2rem 0;
            }
        }

        /* Loading spinner */
        .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: var(--white);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .btn-login.loading .spinner {
            display: inline-block;
            margin-right: 0.5rem;
        }

        .btn-login.loading .btn-text {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <!-- Kenyan Flag Decorative Ribbons -->
        <div class="kenyan-ribbon-1"></div>
        <div class="kenyan-ribbon-2"></div>

        <div class="container">
            <div class="row align-items-center min-vh-100">
                <!-- Left Side - Hero Content -->
                <div class="col-lg-7 col-md-6">
                    <div class="hero-content fade-in-up">
                        <h1 class="hero-title">
                            <i class="fas fa-calculator me-3" style="color: var(--kenya-red);"></i>
                            <span style="color: var(--kenya-white);">Kenyan</span>
                            <span style="color: var(--kenya-green); text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">Payroll</span><br>
                            <span style="color: var(--kenya-white);">Management System</span>
                        </h1>
                        <p class="hero-subtitle">
                            Comprehensive payroll solution designed for Kenyan employment structure 
                            and statutory compliance requirements. Streamline your HR processes with 
                            automated PAYE, NSSF, SHIF, and Housing Levy calculations.
                        </p>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-shield-alt text-white"></i>
                                    </div>
                                    <h5 class="text-white mb-2">Statutory Compliance</h5>
                                    <p class="text-white-50 mb-0">Automated PAYE, NSSF, SHIF & Housing Levy calculations</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                    <h5 class="text-white mb-2">Employee Management</h5>
                                    <p class="text-white-50 mb-0">Complete employee lifecycle management</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-chart-line text-white"></i>
                                    </div>
                                    <h5 class="text-white mb-2">Advanced Reporting</h5>
                                    <p class="text-white-50 mb-0">Generate comprehensive payroll and statutory reports</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-mobile-alt text-white"></i>
                                    </div>
                                    <h5 class="text-white mb-2">Mobile Responsive</h5>
                                    <p class="text-white-50 mb-0">Access your payroll system from any device</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Login Form -->
                <div class="col-lg-5 col-md-6">
                    <div class="login-card fade-in-up-delay">
                        <div class="login-header">
                            <h2 class="login-title">
                                <i class="fas fa-lock text-success me-2"></i>
                                Secure Admin Login
                            </h2>
                            <p class="login-subtitle">Access your payroll management dashboard</p>
                        </div>

                        <form id="loginForm" method="POST" action="login_handler.php">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                                <label for="username">
                                    <i class="fas fa-user me-2"></i>Username
                                </label>
                            </div>

                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                                <label for="password">
                                    <i class="fas fa-key me-2"></i>Password
                                </label>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                                <label class="form-check-label text-muted" for="rememberMe">
                                    Remember me for 30 days
                                </label>
                            </div>

                            <button type="submit" class="btn btn-login" id="loginBtn">
                                <div class="spinner"></div>
                                <span class="btn-text">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Access Dashboard
                                </span>
                            </button>

                            <div class="security-badge">
                                <i class="fas fa-shield-check"></i>
                                <span>256-bit SSL Encrypted Connection</span>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                For demo purposes, use: <strong>admin</strong> / <strong>password</strong>
                            </small>
                        </div>

                        <!-- Kenyan Pride Footer -->
                        <div class="text-center mt-3 p-2" style="background: linear-gradient(90deg, var(--kenya-black) 0%, var(--kenya-red) 25%, var(--kenya-white) 50%, var(--kenya-green) 75%, var(--primary-green) 100%); border-radius: 8px; opacity: 0.8;">
                            <small style="color: var(--white); font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">
                                🇰🇪 Proudly Kenyan • Built for Kenya • Compliant with Kenyan Law 🇰🇪
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check for error messages in URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');
            const errorType = urlParams.get('type');

            if (error) {
                const alertType = errorType === 'lockout' ? 'warning' : 'danger';
                showAlert(decodeURIComponent(error), alertType);

                // Clear URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
            }

            // Auto-focus username field
            document.getElementById('username').focus();
        });

        // Enhanced login form handling
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const loginBtn = document.getElementById('loginBtn');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // Basic validation
            if (!username || !password) {
                e.preventDefault();
                showAlert('Please fill in all fields', 'danger');
                return;
            }

            // Show loading state
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;

            // Form will submit normally to login_handler.php
        });

        // Show alert function
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const form = document.getElementById('loginForm');
            form.insertBefore(alertDiv, form.firstChild);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Enhanced security: Clear form on page unload
        window.addEventListener('beforeunload', function() {
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
        });

        // Prevent right-click context menu on sensitive areas
        document.querySelector('.login-card').addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });



        // Enter key handling
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.id === 'username') {
                    document.getElementById('password').focus();
                } else if (activeElement.id === 'password') {
                    document.getElementById('loginForm').submit();
                }
            }
        });
    </script>
</body>
</html>
